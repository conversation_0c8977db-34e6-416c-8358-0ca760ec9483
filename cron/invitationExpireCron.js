const cron = require("node-cron");
const Invitation = require("../models/User/invitations");
const User = require("../models/User/User");
const socketService = require("../services/socketService");

// Helper to extract Date object for invitation's scheduled time
function getInvitationDateTime(invitation) {
  const date = new Date(invitation.date);
  if (invitation.time) {
    // Support both 'HH:MM' and 'TimeOfDay(HH:MM)' formats
    let hours = 0,
      minutes = 0;
    const match = invitation.time.match(/(\d{1,2}):(\d{2})/);
    if (match) {
      hours = parseInt(match[1]);
      minutes = parseInt(match[2]);
    }
    date.setHours(hours, minutes, 0, 0);
  }
  return date;
}

const invitationExpire = () => {
  // Cron job: runs every 10 minutes
  cron.schedule("*/10 * * * *", async () => {
    try {
      const now = new Date();
      // Find all pending invitations whose scheduled time has passed and are unseen
      const expiredInvitations = await Invitation.find({
        status: "Pending",
        deleted: { $ne: true },
        isSeen: false,
      }).populate("users.userId invitationBy");

      for (const invitation of expiredInvitations) {
        const scheduledDateTime = getInvitationDateTime(invitation);
        if (scheduledDateTime < now) {
          // Mark as expired and deleted
          invitation.status = "Expired";
          invitation.deleted = true;
          invitation.expiredAt = now;
          await invitation.save();

          // Prepare sender info
          const sender = invitation.invitationBy;
          // Find the first invitee who is not the sender
          const invitee = invitation.users
            .map((u) => u.userId)
            .find(
              (u) => u && sender && u._id.toString() !== sender._id.toString()
            );
          const inviteeName = invitee ? invitee.userName : "User";

          // Format date and time for message
          const formattedDate = scheduledDateTime.toLocaleDateString();
          const formattedTime = scheduledDateTime.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });

          // Compose warning message
          const warningMsg = `Your pending request with ${inviteeName} for ${formattedDate} at ${formattedTime} has expired.`;

          // Send real-time warning to sender via socket
          if (sender && sender._id) {
            socketService.emitToUser(
              sender._id.toString(),
              "invitationExpiredWarning",
              { message: warningMsg, invitationId: invitation._id }
            );
          }
          // Optionally, add a user notification or email here as well
        }
      }
    } catch (err) {
      console.error("[Invitation Expiry Cron] Error:", err);
    }
  });
};

module.exports = { invitationExpire }; // For consistency
