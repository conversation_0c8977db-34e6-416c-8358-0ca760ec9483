const User = require("../../models/User/User");
const HelpCenter = require("../../models/User/helpCenter");
const feedback = require("../../models/User/feedback");
const invitation = require("../../models/User/invitations");
const Report = require("../../models/User/reportUser");
const Analytics = require("../../models/Partner/analytics");
const Ratings = require("../../models/Partner/businessRatings");
const { body, validationResult, query } = require("express-validator");
const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const {
  checkAndUpdateEmailVerification,
} = require("../../util/emailVerificationUtils");
const {
  sendMissedMeetingsWarningEmail,
  sendAccountDeletionEmail,
} = require("../../util/meetingNotifications");
const haversine = require("haversine");
const { now } = require("../../util/timeUtils");

module.exports.getUser = async (req, res) => {
  try {
    const userId = req.user._id;

    await User.findByIdAndUpdate(userId, {
      lastOnlineTime: now(),
    });

    await User.updateOne(
      { _id: userId, status: { $exists: false } },
      { $set: { status: [] } }
    );

    const user = await User.aggregate([
      {
        $match: {
          _id: new mongoose.Types.ObjectId(userId),
          deleted: false,
        },
      },
      {
        $lookup: {
          from: "usersubscriptions",
          let: { mainCollectionId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$userId", "$$mainCollectionId"] },
                    { $eq: ["$isActive", true] },
                  ],
                },
              },
            },
          ],
          as: "purchasedSubscriptionInfo",
        },
      },
      {
        $unwind: {
          path: "$purchasedSubscriptionInfo",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "subscriptions",
          localField: "purchasedSubscriptionInfo.subscriptionId",
          foreignField: "_id",
          as: "purchasedSubscriptionPlan",
        },
      },
      {
        $unwind: {
          path: "$purchasedSubscriptionPlan",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "bundles",
          localField: "_id",
          foreignField: "userId",
          as: "bundleInvitation",
        },
      },
      {
        $unwind: {
          path: "$bundleInvitation",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          status: { $ifNull: ["$status", []] },
        },
      },
    ]);

    if (user && user.length > 0) {
      res.status(200).json({ user: user[0] });
    } else throw Error("User not found");
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};

// module.exports.updateUser = async (req, res) => {
//   try {
//     const userId = req.user._id;

// //    console.log("userbody",req.body)
//     const user = await User.findByIdAndUpdate(
//       { _id: userId },
//       { ...req.body },
//       { new: true }
//     );
//     if (user) {
//       res.status(200).json({ user });
//     } else throw Error("User not found");
//   } catch (err) {
//     let error = err.message;
//     res.status(400).json({ error: error });
//   }
// };

module.exports.updateUser = async (req, res) => {
  try {
    // console.log("req.body", req.body);
    const userId = req.user._id;
    const { email, latitude, longitude, ...updateFields } = req.body;

    const updateObject = { ...updateFields };

    if (latitude !== undefined && longitude !== undefined) {
      updateObject.location = {
        type: "Point",
        coordinates: [longitude, latitude],
      };
    }

    if (email) {
      await checkAndUpdateEmailVerification(email);
      const existingUser = await User.findOne({ email });
      if (!existingUser) {
        updateObject.email = email;
        updateObject.isEmailVerified = false;
      }
    }
    // console.log("updateObject", updateObject);
    const user = await User.findByIdAndUpdate(
      { _id: userId },
      { $set: updateObject },
      { new: true }
    );

    if (user) {
      res.status(200).json({ user });
    } else {
      throw new Error("User not found");
    }
  } catch (err) {
    // Handle errors and respond with appropriate status and message
    const error = err.message;
    console.log("error", error);
    res.status(400).json({ error: error });
  }
};
module.exports.updatePassword = [
  body("oldPassword")
    .not()
    .isEmpty()
    .withMessage("oldPassword Field is required"),
  body("newPassword")
    .not()
    .isEmpty()
    .withMessage("newPassword Field is required"),
  async (req, res) => {
    const { newPassword, oldPassword } = req.body;
    let userId = req.user._id;
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }
      const user = await User.findOne({ _id: userId });
      if (user) {
        const auth = await bcrypt.compare(oldPassword, user.password);
        if (!auth) {
          res.status(400).json({ message: "Please Enter Correct Password" });
        } else {
          const salt = await bcrypt.genSalt();
          const hashedPassword = await bcrypt.hash(newPassword, salt);
          const updatePassword = await User.findByIdAndUpdate(
            { _id: req.user._id },
            { password: hashedPassword },
            { new: true, useFindAndModify: false }
          );
          res.status(200).json({
            message: "Password Updated Successfuly",
            user: updatePassword,
          });
        }
      } else throw Error("User not found");
    } catch (err) {
      console.log(err);
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.getUsersUnderRadius = async (req, res) => {
  try {
    // console.log("req.body", req.body);
    const {
      coordinates,
      radius,
      tagsid,
      ageRangeStart,
      ageRangeEnd,
      gender,
      statusid,
      isEmailVerified,
    } = req.body;
    const currentDate = now();
    let obj = {};
    if (ageRangeStart) {
      obj["dob"] = {
        $lte: new Date(currentDate.getUTCFullYear() - ageRangeStart + 1, 0, 1),
      };
    }
    if (ageRangeEnd) {
      obj["dob"] = {
        $gte: new Date(currentDate.getUTCFullYear() - ageRangeEnd, 0, 1),
      };
    }
    if (ageRangeStart && ageRangeEnd) {
      obj["dob"] = {
        $gte: new Date(currentDate.getUTCFullYear() - ageRangeEnd, 0, 1),
        $lte: new Date(currentDate.getUTCFullYear() - ageRangeStart + 1, 0, 1),
      };
    }

    if (gender) {
      obj["gender"] = gender;
    }
    if (isEmailVerified == true) {
      obj["isEmailVerified"] = isEmailVerified;
    }
    if (statusid && Array.isArray(statusid)) {
      const statusArray = statusid.map((id) => id.toString());
      obj["status._id"] = { $all: statusArray };
    }
    if (tagsid && Array.isArray(tagsid)) {
      const tagsArray = tagsid.map((id) => id.toString());
      obj["tags._id"] = { $all: tagsArray };
    }

    if (req.user) {
      await User.findByIdAndUpdate(req.user._id, {
        lastOnlineTime: now(),
      });
    }

    // const query = [
    //   {
    //     $geoNear: {
    //       near: {
    //         type: "Point",
    //         coordinates: coordinates,
    //       },
    //       distanceField: "distance",
    //       maxDistance: Number(radius),
    //       spherical: true,
    //     },
    //   },
    //   {
    //     $lookup: {
    //       from: "reports",
    //       let: { userId: "$_id" },
    //       pipeline: [
    //         {
    //           $match: {
    //             $expr: {
    //               $and: [
    //                 { $eq: ["$reportedTo", "$$userId"] },
    //                 { $eq: ["$report", "true"] },
    //               ],
    //             },
    //           },
    //         },
    //       ],
    //       as: "reportedUsers",
    //     },
    //   },
    //   {
    //     $match: {
    //       reportedUsers: { $size: 0 },
    //       ...obj,
    //       deleted: false,
    //       coverImage: { $exists: true, $ne: "" },
    //     },
    //   },
    //   {
    //     $project: {
    //       password: 0,
    //       reportedUsers: 0,
    //     },
    //   },
    // ];
    const query = [
      {
        $geoNear: {
          near: {
            type: "Point",
            coordinates: coordinates,
          },
          distanceField: "distance",
          maxDistance: Number(radius),
          spherical: true,
        },
      },
      {
        $lookup: {
          from: "reports",
          let: { userId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $or: [
                        // Check if current user is reported
                        {
                          $and: [
                            { $eq: ["$reportedTo", "$$userId"] },
                            {
                              $eq: [
                                "$reportedBy",
                                mongoose.Types.ObjectId(req?.user?._id),
                              ],
                            }, // Current user's ObjectId
                            { $eq: ["$report", "true"] },
                          ],
                        },
                        // Check if current user is reporter
                        {
                          $and: [
                            { $eq: ["$reportedBy", "$$userId"] },
                            {
                              $eq: [
                                "$reportedTo",
                                mongoose.Types.ObjectId(req?.user?._id),
                              ],
                            },
                            { $eq: ["$report", "true"] },
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            },
          ],
          as: "reportedUsers",
        },
      },
      {
        $match: {
          reportedUsers: { $size: 0 }, // Only include users with no reporting relationship
          ...obj,
          deleted: false,
          coverImage: { $exists: true, $ne: "" },
        },
      },
      {
        $project: {
          password: 0,
          reportedUsers: 0,
        },
      },
    ];

    if (req.user) {
      query.push({
        $match: {
          _id: { $ne: mongoose.Types.ObjectId(req.user._id) },
        },
      });
    }

    const user = await User.aggregate(query);

    if (user.length > 0) {
      // Sort users based on lastOnlineTime and premium status
      user.sort((a, b) => {
        // Handle cases where lastOnlineTime might be undefined
        const aLastOnline = a.lastOnlineTime
          ? a.lastOnlineTime.getTime()
          : -Infinity;
        const bLastOnline = b.lastOnlineTime
          ? b.lastOnlineTime.getTime()
          : -Infinity;

        if (aLastOnline !== bLastOnline) {
          // Sort by lastOnlineTime (most recent first)
          return bLastOnline - aLastOnline;
        }

        // If lastOnlineTime is the same, prioritize premium users
        if (a.isPremium && !b.isPremium) return -1;
        if (!a.isPremium && b.isPremium) return 1;

        return 0; // Same premium status, maintain relative order
      });

      // console.log("user.length", user.length);
      res.status(200).json({ user });
    } else {
      throw new Error("No users found within the specified radius");
    }
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
module.exports.getUserById = [
  query("userId").not().isEmpty().withMessage("userId Field is required"),
  async (req, res) => {
    try {
      const { userId } = req.query;
      // console.log("userId", userId);
      const currentUser = await User.findById(req.user._id);
      const user = await User.findById(userId);
      // console.log("user", user);
      if (!user) {
        throw new Error(
          "This user has been deleted by admin. Please contact admin."
        );
      }
      const userLatLon = {
        latitude: user.location?.coordinates[0], // Latitude
        longitude: user.location?.coordinates[1], // Longitude
      };
      const currentUserLatLon = {
        latitude: currentUser.location?.coordinates[0], // Latitude
        longitude: currentUser.location?.coordinates[1], // Longitude
      };
      const inviteUserDistance = haversine(currentUserLatLon, userLatLon, {
        unit: "km",
      });
      // console.log("inviteUserDistance", inviteUserDistance);
      if (user) {
        // const token = await createToken(user);
        res.status(200).json({
          user: {
            ...user.toObject(),
            distance: inviteUserDistance,
          },
        });
      } else throw new Error("User not found");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.getSpecificUsers = [
  async (req, res) => {
    try {
      const { userIds } = req.body;
      const user = await User.find({
        _id: { $in: userIds },
      });
      if (user) {
        res.status(200).json({ user });
      } else throw Error("User not found");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

// module.exports.getSpecificUsers = [
//   async (req, res) => {
//     try {
//       const { userIds } = req.body;
//       const user = await User.find({
//         _id: { $in: userIds },
//       });
//       if (user) {
//         res.status(200).json({ user });
//       } else throw Error("User not found");
//     } catch (err) {
//       let error = err.message;
//       res.status(400).json({ error: error });
//     }
//   },
// ];

module.exports.updateBusinessRating = [
  body("userId").not().isEmpty().withMessage("userId Field is required"),
  body("businessId")
    .not()
    .isEmpty()
    .withMessage("businessId Field is required"),
  body("rating").not().isEmpty().withMessage("rating Field is required"),
  async (req, res) => {
    try {
      const { userId, businessId } = req.body;
      const ratings = await Ratings.findOneAndUpdate(
        { userId, businessId },
        { ...req.body },
        { new: true, upsert: true }
      );
      if (ratings) {
        // const token = await createToken(user);
        res.status(200).json({ data: ratings });
      } else throw Error("Data not found");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.createHelpCenter = [
  body("name").not().isEmpty().withMessage("name field is required"),
  body("email").not().isEmpty().withMessage("email Field is required"),
  body("subject").not().isEmpty().withMessage("subject Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      const helpcenter = await HelpCenter.create({
        userId: req.user._id,
        message: req.body.message,
        ...req.body,
      });
      res.status(201).json({ HelpCenter: helpcenter });
    } catch (err) {
      console.log(err, "error");
    }
  },
];

module.exports.createFeedback = [
  async (req, res) => {
    try {
      const userFeedback = await feedback.create({
        userId: req.user._id,
        invitationId: req.params.invitationId,
        ...req.body,
      });

      await invitation.findByIdAndUpdate(req.params.invitationId, {
        $set: { feedbackId: userFeedback._id },
      });

      if (userFeedback.status == "no") {
        // Increment the didNotAttendMeetingCount for the user who didn't attend
        const updateCount = await User.findOneAndUpdate(
          { _id: req.body.feedbackFor },
          { $inc: { didNotAttendMeetingCount: 1 } },
          { new: true }
        );

        // If user has missed exactly 3 meetings, send a warning email
        if (updateCount.didNotAttendMeetingCount === 3) {
          try {
            await sendMissedMeetingsWarningEmail(updateCount);
            console.log(
              `Warning email sent to user ${updateCount._id} for missing 3 meetings`
            );

            // Create a notification record in the database if you have a notification system
            // This is optional based on your application's notification system
            // await Notification.create({
            //   userId: updateCount._id,
            //   type: 'warning',
            //   title: 'Meeting Attendance Warning',
            //   message: 'You have missed 3 meetings. One more missed meeting may result in account deactivation.',
            //   read: false
            // });
          } catch (emailError) {
            console.error("Failed to send warning email:", emailError);
            // Continue execution even if email fails
          }
        }

        // If user has missed more than 4 meetings, deactivate their account
        if (updateCount.didNotAttendMeetingCount > 4) {
          try {
            // Send account deletion notification email
            await sendAccountDeletionEmail(updateCount);
            console.log(
              `Account deletion email sent to user ${updateCount._id}`
            );

            // Mark the user as deleted (soft delete)
            await User.updateOne(
              { _id: updateCount._id },
              {
                deleted: true,
                deletedAt: now(),
                deletedReason: "Missed more than 4 meetings",
              }
            );

            console.log(
              `User ${updateCount._id} has been marked as deleted due to missing more than 4 meetings`
            );
          } catch (emailError) {
            console.error("Failed to send account deletion email:", emailError);
            // Still mark the user as deleted even if email fails
            await User.updateOne(
              { _id: updateCount._id },
              {
                deleted: true,
                deletedAt: now(),
                deletedReason: "Missed more than 4 meetings",
              }
            );
          }
        }
      }

      res.status(201).json({ Feedback: userFeedback });
    } catch (err) {
      console.log(err, "error");
    }
  },
];

module.exports.reportUser = async (req, res) => {
  try {
    const reportuser = await Report.create({
      reportedBy: req.user._id,
      reportedTo: req.body.reportedTo,
      hide: req.body.hide,
      report: req.body.report,
      block: req.body.block,
      categories: req.body.categories,
      message: req.body.message,
    });

    res.status(200).json({ Report: reportuser });
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};

module.exports.getReportedUserById = async (req, res) => {
  try {
    const getReportedUserById = await Report.find({ reportedBy: req.user._id });
    if (!getReportedUserById) {
      res.status(400).json({ message: "user does not exist" });
    } else {
      res.status(200).json({ ReportDetails: getReportedUserById });
    }
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};

module.exports.updateAnalytics = async (req, res) => {
  try {
    // console.log("req.body", req.body);
    await Analytics.create({
      ...req.body,
      userId: req.user._id,
      actionTime: now(), // Store in UTC
    });
    res.status(200).json({ message: "Added analytics" });
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};
