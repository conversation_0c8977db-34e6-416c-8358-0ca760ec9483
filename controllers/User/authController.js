const User = require("../../models/User/User");
const bcrypt = require("bcryptjs");
const {
  createToken,
  createTokenPair,
} = require("../../middleware/createToken");
const { body, validationResult } = require("express-validator");
const { sendEmail } = require("../../util/sendEmail");
const {
  checkAndUpdateEmailVerification,
} = require("../../util/emailVerificationUtils");

// Helper function to get client IP and user agent
const getClientInfo = (req) => {
  return {
    ipAddress: req.ip || req.connection.remoteAddress,
    userAgent: req.headers["user-agent"] || "",
  };
};

module.exports.register = [
  body("email").not().isEmpty().withMessage("email Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      let findUser = await User.findOne({ email: req.body.email });
      if (!findUser) {
        const hashedPassword = await bcrypt.hash(req.body.password, 10);
        req.body.password = hashedPassword;

        const user = await User.create({ ...req.body });

        // For backward compatibility
        const token = await createToken(user);

        // Generate new token pair with refresh token
        const clientInfo = getClientInfo(req);
        try {
          const tokenPair = await createTokenPair(user, "user", clientInfo);

          // Check if there was an error creating the refresh token
          if (tokenPair.error) {
            console.warn(
              "Error creating refresh token during registration:",
              tokenPair.error
            );
            // Fall back to just using the legacy token
            const mailOptions = {
              from: '"NETME App" <' + process.env.USER_APP_EMAIL + ">",
              to: req.body.email,
              subject: "Welcome to Our Platform!",
              text: `Hello,\n
              Thank you for registering on our platform! We're excited to have you on board.\n
              Your account has been created and you can now log in using your credentials.\n
              Thank you and Best regards\n
              Netme`,
            };
            await sendEmail(mailOptions);
            return res.status(201).json({
              user: user,
              token: token,
              accessToken: tokenPair.accessToken,
              refreshTokenError: tokenPair.error,
            });
          }

          // Set refresh token as HTTP-only cookie for better security
          const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production", // Use secure in production
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
            path: "/",
          };

          res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

          const mailOptions = {
            from: '"NETME App" <' + process.env.USER_APP_EMAIL + ">",
            to: req.body.email,
            subject: "Welcome to Our Platform!",
            text: `Hello,\n
            Thank you for registering on our platform! We're excited to have you on board.\n
            Your account has been created and you can now log in using your credentials.\n
            Thank you and Best regards\n
            Netme`,
          };
          await sendEmail(mailOptions);

          // Return both tokens in response
          res.status(201).json({
            user: user,
            token: token, // Legacy token
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
          });
        } catch (tokenError) {
          console.error(
            "Unexpected error creating tokens during registration:",
            tokenError
          );
          // Fall back to just using the legacy token
          const mailOptions = {
            from: '"NETME App" <' + process.env.USER_APP_EMAIL + ">",
            to: req.body.email,
            subject: "Welcome to Our Platform!",
            text: `Hello,\n
            Thank you for registering on our platform! We're excited to have you on board.\n
            Your account has been created and you can now log in using your credentials.\n
            Thank you and Best regards\n
            Netme`,
          };
          await sendEmail(mailOptions);
          res.status(201).json({
            user: user,
            token: token, // Legacy token
            refreshTokenError: "Failed to create refresh token",
          });
        }
      } else
        throw Error(`User Already Loginned with ${findUser.verificationType}`);
    } catch (err) {
      let error = err.message;
      if (err.code == 11000) {
        error = ` Email already exists`;
      }
      res.status(400).json({ error: error });
    }
  },
];

module.exports.login = [
  body("email").not().isEmpty().withMessage("Email field is required"),
  body("password").not().isEmpty().withMessage("Password field is required"),
  async (req, res) => {
    //console.log("req.body", req.body);
    const { email, password, fcm } = req.body;
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Check and update email verification status
      await checkAndUpdateEmailVerification(email);

      const user = await User.findOne({ email });

      if (user) {
        // Verify password
        const auth = await bcrypt.compare(password, user.password);
        if (auth) {
          if (fcm) {
            await User.updateMany(
              { fcmToken: fcm, _id: { $ne: user._id } },
              { $set: { fcmToken: null } }
            );

            // Update current user's fcmtoken
            //await User.findByIdAndUpdate(user._id, { fcmtoken }, { new: true });
          }
          // Generate tokens with updated user data
          const updatedUser = await User.findOne({ email }).select("-password");

          // For backward compatibility
          const token = await createToken(updatedUser);

          // Generate new token pair with refresh token
          const clientInfo = getClientInfo(req);
          try {
            const tokenPair = await createTokenPair(
              updatedUser,
              "user",
              clientInfo
            );

            // Check if there was an error creating the refresh token
            if (tokenPair.error) {
              console.warn("Error creating refresh token:", tokenPair.error);
              // Fall back to just using the legacy token
              return res.status(200).json({
                user: updatedUser,
                token, // Legacy token
                accessToken: tokenPair.accessToken,
                refreshTokenError: tokenPair.error,
              });
            }

            // Set refresh token as HTTP-only cookie for better security
            const cookieOptions = {
              httpOnly: true,
              secure: process.env.NODE_ENV === "production", // Use secure in production
              maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
              path: "/",
            };

            res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

            // Return both tokens in response for flexibility
            res.status(200).json({
              user: updatedUser,
              token, // Legacy token
              accessToken: tokenPair.accessToken,
              refreshToken: tokenPair.refreshToken,
            });
          } catch (tokenError) {
            console.error("Unexpected error creating tokens:", tokenError);
            // Fall back to just using the legacy token
            res.status(200).json({
              user: updatedUser,
              token, // Legacy token
              refreshTokenError: "Failed to create refresh token",
            });
          }
        } else {
          throw new Error("Incorrect password");
        }
      } else {
        throw new Error("Email ID is not connected to any user");
      }
    } catch (err) {
      console.log(err);
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.googleAuth = [
  body("email").not().isEmpty().withMessage("email Field is required"),
  async (req, res) => {
    const { email } = req.body;
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      await checkAndUpdateEmailVerification(email);
      const findUser = await User.findOne({ email });
      if (findUser) {
        if (findUser.verificationType !== "GOOGLE") {
          throw Error(
            `Please Use ${findUser.verificationType ?? "EMAIL"} to login`
          );
        } else {
          // For backward compatibility
          const token = await createToken(findUser);

          // Generate new token pair with refresh token
          const clientInfo = getClientInfo(req);
          const tokenPair = await createTokenPair(findUser, "user", clientInfo);

          // Set refresh token as HTTP-only cookie
          const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            path: "/",
          };

          res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

          res.status(200).json({
            user: findUser,
            token, // Legacy token
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
          });
        }
      } else {
        const createUser = await User.create({
          email,
          verificationType: "GOOGLE",
        });
        if (createUser) {
          // For backward compatibility
          const token = await createToken(createUser);

          // Generate new token pair with refresh token
          const clientInfo = getClientInfo(req);
          const tokenPair = await createTokenPair(
            createUser,
            "user",
            clientInfo
          );

          // Set refresh token as HTTP-only cookie
          const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            path: "/",
          };

          res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

          res.status(200).json({
            user: createUser,
            token, // Legacy token
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
          });
        }
      }
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.socialAuth = [
  body("email").not().isEmpty().withMessage("email Field is required"),
  async (req, res) => {
    const { email, type, appleUniqueKey } = req.body;
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }
      const findUser = await User.findOne({ email });
      if (findUser) {
        if (findUser.verificationType !== type) {
          throw Error(
            `An Account is already linked to this Email. Please login`
          );
        } else {
          // For backward compatibility
          const token = await createToken(findUser);

          // Generate new token pair with refresh token
          const clientInfo = getClientInfo(req);
          const tokenPair = await createTokenPair(findUser, "user", clientInfo);

          // Set refresh token as HTTP-only cookie
          const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            path: "/",
          };

          res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

          res.status(200).json({
            user: findUser,
            token, // Legacy token
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
          });
        }
      } else {
        const createUser = await User.create({
          ...req.body,
          verificationType: type,
        });
        if (createUser) {
          // For backward compatibility
          const token = await createToken(createUser);

          // Generate new token pair with refresh token
          const clientInfo = getClientInfo(req);
          const tokenPair = await createTokenPair(
            createUser,
            "user",
            clientInfo
          );

          // Set refresh token as HTTP-only cookie
          const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            path: "/",
          };

          res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

          res.status(200).json({
            user: createUser,
            token, // Legacy token
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
          });
        }
      }
    } catch (err) {
      let error = err.message;
      console.log("error", error);
      res.status(400).json({ error: error });
    }
  },
];

module.exports.facebookAuth = [
  body("email").not().isEmpty().withMessage("email Field is required"),
  async (req, res) => {
    const { email, userName } = req.body;
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      await checkAndUpdateEmailVerification(email);
      const findUser = await User.findOne({ email });
      if (findUser) {
        if (findUser.verificationType !== "FACEBOOK") {
          throw Error(
            `An Account is already linked to this Email. Please login`
          );
        } else {
          // For backward compatibility
          const token = await createToken(findUser);

          // Generate new token pair with refresh token
          const clientInfo = getClientInfo(req);
          const tokenPair = await createTokenPair(findUser, "user", clientInfo);

          // Set refresh token as HTTP-only cookie
          const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            path: "/",
          };

          res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

          res.status(200).json({
            user: findUser,
            token, // Legacy token
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
          });
        }
      } else {
        const createUser = await User.create({
          email,
          userName,
          verificationType: "FACEBOOK",
        });
        if (createUser) {
          // For backward compatibility
          const token = await createToken(createUser);

          // Generate new token pair with refresh token
          const clientInfo = getClientInfo(req);
          const tokenPair = await createTokenPair(
            createUser,
            "user",
            clientInfo
          );

          // Set refresh token as HTTP-only cookie
          const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            path: "/",
          };

          res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

          res.status(200).json({
            user: createUser,
            token, // Legacy token
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
          });
        }
      }
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.forgotPassword = [
  body("email").not().isEmpty().withMessage("email Field is required"),
  body("password").not().isEmpty().withMessage("password Field is required"),
  async (req, res) => {
    const { email, password } = req.body;
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }
      const user = await User.findOne({ email });
      if (user) {
        // Check if user has a password set (might not if they used social auth)
        if (user.password) {
          try {
            const auth = await bcrypt.compare(password, user.password);
            if (auth) {
              return res
                .status(400)
                .json({ message: "Old and New password can't be same" });
            }
          } catch (compareError) {
            console.error("Error comparing passwords:", compareError);
            // Continue with password update even if comparison fails
          }
        }

        // Update the password
        const salt = await bcrypt.genSalt();
        const hashedPassword = await bcrypt.hash(password, salt);
        const updatePassword = await User.findOneAndUpdate(
          { email },
          { password: hashedPassword },
          { new: true, useFindAndModify: false }
        );

        // Revoke all refresh tokens for this user for security
        try {
          const RefreshToken = require("../../models/User/RefreshToken");
          await RefreshToken.updateMany(
            { userId: user._id, userModel: "user" },
            { isRevoked: true }
          );
        } catch (tokenError) {
          console.error("Error revoking refresh tokens:", tokenError);
          // Continue even if token revocation fails
        }

        res.status(200).json({
          message: "Password Updated Successfully",
          user: updatePassword,
        });
      } else throw Error("User not found with given Email");
    } catch (err) {
      console.error("Error in forgotPassword:", err);
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.checkUserEmail = [
  body("email").not().isEmpty().withMessage("email Field is required"),

  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    const { email } = req.body;
    try {
      let findUser = await User.findOne({ email, deleted: false });
      if (findUser) {
        res
          .status(200)
          .json({ status: true, message: "Email already registered" });
      } else {
        res
          .status(200)
          .json({ status: false, message: "Email Not registered" });
      }
    } catch (err) {
      let error = err.message;
      if (err.code == 11000) {
        error = ` Email already exists`;
      }
      res.status(400).json({ error: error });
    }
  },
];

module.exports.checkUserAppleKey = [
  body("appleUniqueKey")
    .not()
    .isEmpty()
    .withMessage("appleUniqueKey Field is required"),

  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    const { appleUniqueKey } = req.body;
    try {
      let findUser = await User.findOne({
        appleUniqueKey,
      });
      if (findUser) {
        res
          .status(200)
          .json({ status: true, message: "Key already registered" });
      } else {
        res.status(200).json({ status: false, message: "Key Not registered" });
      }
    } catch (err) {
      let error = err.message;
      if (err.code == 11000) {
        error = ` Email already exists`;
      }
      res.status(400).json({ error: error });
    }
  },
];

module.exports.appleAuth = [
  body("appleUniqueKey")
    .not()
    .isEmpty()
    .withMessage("appleUniqueKey Field is required"),
  async (req, res) => {
    // console.log("req.body", req.body);
    const { email, type, appleUniqueKey } = req.body;
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      await checkAndUpdateEmailVerification(email);

      const findUser = await User.findOne({ appleUniqueKey });
      if (findUser) {
        if (findUser.verificationType !== type) {
          throw Error(
            `An Account is already linked to this Email. Please login`
          );
        } else {
          // For backward compatibility
          const token = await createToken(findUser);

          // Generate new token pair with refresh token
          const clientInfo = getClientInfo(req);
          const tokenPair = await createTokenPair(findUser, "user", clientInfo);

          // Set refresh token as HTTP-only cookie
          const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            path: "/",
          };

          res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

          res.status(200).json({
            user: findUser,
            token, // Legacy token
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
          });
        }
      } else {
        const createUser = await User.create({
          ...req.body,
          verificationType: type,
        });
        if (createUser) {
          // For backward compatibility
          const token = await createToken(createUser);

          // Generate new token pair with refresh token
          const clientInfo = getClientInfo(req);
          const tokenPair = await createTokenPair(
            createUser,
            "user",
            clientInfo
          );

          // Set refresh token as HTTP-only cookie
          const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            path: "/",
          };

          res.cookie("refreshToken", tokenPair.refreshToken, cookieOptions);

          res.status(200).json({
            user: createUser,
            token, // Legacy token
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
          });
        }
      }
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
