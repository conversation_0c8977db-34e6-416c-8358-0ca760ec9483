/**
 * Middleware to automatically format all date/time fields in API responses to ISO 8601 UTC format
 * This ensures consistent timezone handling across all API endpoints
 */

const { formatForAPI } = require('../util/timeUtils');

/**
 * Recursively traverse an object and format all Date objects to ISO 8601 UTC strings
 * @param {*} obj - Object to traverse and format
 * @returns {*} - Object with formatted dates
 */
function formatDatesInObject(obj) {
  if (obj === null || obj === undefined) {
    return obj;
  }

  // If it's a Date object, format it to ISO 8601 UTC
  if (obj instanceof Date) {
    return formatForAPI(obj);
  }

  // If it's an array, recursively format each element
  if (Array.isArray(obj)) {
    return obj.map(item => formatDatesInObject(item));
  }

  // If it's an object (but not Date, Array, or null), recursively format its properties
  if (typeof obj === 'object') {
    const formatted = {};
    for (const [key, value] of Object.entries(obj)) {
      // Handle common date field names
      if (isDateField(key) && value instanceof Date) {
        formatted[key] = formatForAPI(value);
      } else {
        formatted[key] = formatDatesInObject(value);
      }
    }
    return formatted;
  }

  // For primitive types, return as-is
  return obj;
}

/**
 * Check if a field name indicates it contains a date/time value
 * @param {string} fieldName - Name of the field
 * @returns {boolean} - True if field likely contains a date/time
 */
function isDateField(fieldName) {
  const dateFieldPatterns = [
    /.*at$/i,           // createdAt, updatedAt, deletedAt, etc.
    /.*time$/i,         // lastOnlineTime, chatOpenTime, etc.
    /^date$/i,          // date field
    /.*date$/i,         // releaseDate, expireDate, etc.
    /timestamp/i,       // timestamp fields
    /^time$/i,          // time field
    /joinedAt/i,        // joinedAt
    /acceptedAt/i,      // acceptedAt
    /sentAt/i,          // sentAt
    /expiredAt/i,       // expiredAt
    /scheduledTime/i,   // scheduledTime
    /chatOpenTime/i,    // chatOpenTime
    /chatCloseTime/i,   // chatCloseTime
  ];

  return dateFieldPatterns.some(pattern => pattern.test(fieldName));
}

/**
 * Middleware function to format time fields in API responses
 */
function formatTimeResponse(req, res, next) {
  // Store the original res.json method
  const originalJson = res.json;

  // Override res.json to format dates before sending
  res.json = function(body) {
    try {
      // Only format if body is an object (not string, number, etc.)
      if (body && typeof body === 'object') {
        const formattedBody = formatDatesInObject(body);
        return originalJson.call(this, formattedBody);
      } else {
        return originalJson.call(this, body);
      }
    } catch (error) {
      console.error('Error formatting dates in API response:', error);
      // If formatting fails, send original response
      return originalJson.call(this, body);
    }
  };

  next();
}

/**
 * Utility function to manually format a response object
 * Use this when you need to format dates before sending a response
 * @param {*} data - Data to format
 * @returns {*} - Formatted data
 */
function formatResponseData(data) {
  return formatDatesInObject(data);
}

module.exports = {
  formatTimeResponse,
  formatResponseData,
  formatDatesInObject,
  isDateField
};
