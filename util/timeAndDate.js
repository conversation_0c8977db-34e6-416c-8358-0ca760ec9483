function extractTimeFromISOString(isoString) {
  const date = new Date(isoString);

  // Extract UTC hours, minutes, seconds
  const hours = String(date.getUTCHours()).padStart(2, "0");
  const minutes = String(date.getUTCMinutes()).padStart(2, "0");
  //   const seconds = String(date.getUTCSeconds()).padStart(2, "0");

  return `${hours}:${minutes}`;
}

module.exports = {
  extractTimeFromISOString,
};
