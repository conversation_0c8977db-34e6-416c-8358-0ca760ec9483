const { default: mongoose } = require("mongoose");
const Invitation = require("../models/User/invitations");

// module.exports.getAggregatedInvitaion = async ({
//   skipValue,
//   limit,
//   aggObj = {},
// }) => {
//   const data = await Invitation.aggregate([
//     {
//       $match: {
//         deleted: false,
//       },
//     },
//     {
//       $lookup: {
//         from: "users",
//         localField: "rescheduleBy",
//         foreignField: "_id",
//         as: "rescheduleBy",
//       },
//     },
//     {
//       $unwind: {
//         path: "$rescheduleBy",
//         preserveNullAndEmptyArrays: true,
//       },
//     },
//     {
//       $lookup: {
//         from: "feedbacks",
//         localField: "feedbackId",
//         foreignField: "_id",
//         as: "feedbackId",
//       },
//     },
//     {
//       $unwind: {
//         path: "$feedbackId",
//         preserveNullAndEmptyArrays: true,
//       },
//     },
//     {
//       $lookup: {
//         from: "users",
//         localField: "invitationBy",
//         foreignField: "_id",
//         as: "invitationBy",
//       },
//     },
//     {
//       $unwind: {
//         path: "$invitationBy",
//         preserveNullAndEmptyArrays: true,
//       },
//     },
//     {
//       $lookup: {
//         from: "partnerbusinesses",
//         localField: "businessId",
//         foreignField: "_id",
//         as: "businessData",
//       },
//     },
//     {
//       $unwind: {
//         path: "$businessData",
//         preserveNullAndEmptyArrays: true,
//       },
//     },
//     {
//       $unwind: {
//         path: "$users",
//         preserveNullAndEmptyArrays: true,
//       },
//     },
//     {
//       $lookup: {
//         from: "users",
//         localField: "users.userId",
//         foreignField: "_id",
//         as: "users.userId",
//       },
//     },
//     {
//       $unwind: {
//         path: "$users.userId",
//         preserveNullAndEmptyArrays: true,
//       },
//     },
//     {
//       $addFields: {
//         businessId: {
//           $ifNull: ["$businessData", "$googleBusiness"],
//         },
//       },
//     },
//     {
//       $group: {
//         _id: "$_id",
//         users: { $push: "$users" },
//         groupName: { $first: "$groupName" },
//         dob: { $first: "$dob" },
//         isSeen: { $first: "$isSeen" },
//         invitationBy: { $first: "$invitationBy" },
//         feedbackId: { $first: "$feedbackId" },
//         businessId: { $first: "$businessId" },
//         date: { $first: "$date" },
//         time: { $first: "$time" },
//         isGroup: { $first: "$isGroup" },
//         isRescheduled: { $first: "$isRescheduled" },
//         rescheduleBy: { $first: "$rescheduleBy" },
//         status: { $first: "$status" },
//         reschedule: { $first: "$reschedule" },
//         createdAt: { $first: "$createdAt" },
//       },
//     },
//     {
//       $match: {
//         ...aggObj,
//       },
//     },
//     {
//       $sort: { createdAt: -1 },
//     },
//     {
//       $skip: skipValue,
//     },
//     {
//       $limit: limit,
//     },
//   ]);

//   return data;
// };

module.exports.getAggregatedInvitaion = async ({
  skipValue,
  limit,
  aggObj = {},
}) => {
  const data = await Invitation.aggregate([
    {
      $match: {
        deleted: false,
      },
    },
    {
      $lookup: {
        from: "users",
        localField: "rescheduleBy",
        foreignField: "_id",
        as: "rescheduleBy",
      },
    },
    {
      $unwind: {
        path: "$rescheduleBy",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "feedbacks",
        localField: "feedbackId",
        foreignField: "_id",
        as: "feedbackId",
      },
    },
    {
      $unwind: {
        path: "$feedbackId",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "users",
        localField: "invitationBy",
        foreignField: "_id",
        as: "invitationBy",
      },
    },
    {
      $unwind: {
        path: "$invitationBy",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "partnerbusinesses",
        localField: "businessId",
        foreignField: "_id",
        as: "businessData",
      },
    },
    {
      $unwind: {
        path: "$businessData",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: "$users",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "users",
        localField: "users.userId",
        foreignField: "_id",
        as: "users.userId",
      },
    },
    {
      $unwind: {
        path: "$users.userId",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        businessId: {
          $ifNull: ["$businessData", "$googleBusiness"],
        },
      },
    },
    {
      $group: {
        _id: "$_id",
        users: { $push: "$users" },
        groupName: { $first: "$groupName" },
        dob: { $first: "$dob" },
        isSeen: { $first: "$isSeen" },
        note: { $first: "$note" },
        isDeleted: { $first: "$isDeleted" },
        invitationBy: { $first: "$invitationBy" },
        feedbackId: { $first: "$feedbackId" },
        businessId: { $first: "$businessId" },
        date: { $first: "$date" },
        time: { $first: "$time" },
        isGroup: { $first: "$isGroup" },
        isRescheduled: { $first: "$isRescheduled" },
        rescheduleBy: { $first: "$rescheduleBy" },
        status: { $first: "$status" },
        reschedule: { $first: "$reschedule" },
        createdAt: { $first: "$createdAt" },
        isPremium: { $first: "$invitationBy.isPremium" },
      },
    },
    {
      $addFields: {
        isPremiumRecentlySent: {
          $and: [
            { $eq: ["$isPremium", true] },
            { $gte: [{ $subtract: [new Date(), "$createdAt"] }, 0] },
            {
              $lt: [
                { $subtract: [new Date(), "$createdAt"] },
                24 * 60 * 60 * 1000,
              ],
            }, // 24 hours in milliseconds
          ],
        },
      },
    },
    {
      $sort: {
        isPremiumRecentlySent: -1,
        createdAt: -1,
      },
    },
    {
      $match: {
        ...aggObj,
      },
    },
    {
      $skip: skipValue,
    },
    {
      $limit: limit,
    },
  ]);

  return data;
};

module.exports.getAggregatedInvitationsRequests = async ({
  skipValue,
  limit,
  userId,
  aggObj = {},
}) => {
  console.log("aggObj", aggObj);
  console.log("UserId", userId);

  const data = await Invitation.aggregate([
    {
      $lookup: {
        from: "users",
        localField: "rescheduleBy",
        foreignField: "_id",
        as: "rescheduleBy",
      },
    },
    { $unwind: { path: "$rescheduleBy", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "feedbacks",
        localField: "feedbackId",
        foreignField: "_id",
        as: "feedbackId",
      },
    },
    { $unwind: { path: "$feedbackId", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "users",
        localField: "invitationBy",
        foreignField: "_id",
        as: "invitationBy",
      },
    },
    { $unwind: { path: "$invitationBy", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "partnerbusinesses",
        localField: "businessId",
        foreignField: "_id",
        as: "businessData",
      },
    },
    { $unwind: { path: "$businessData", preserveNullAndEmptyArrays: true } },
    { $unwind: { path: "$users", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "users",
        localField: "users.userId",
        foreignField: "_id",
        as: "users.userId",
      },
    },
    { $unwind: { path: "$users.userId", preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        businessId: { $ifNull: ["$businessData", "$googleBusiness"] },
      },
    },
    {
      $group: {
        _id: "$_id",
        users: { $push: "$users" },
        groupName: { $first: "$groupName" },
        dob: { $first: "$dob" },
        isSeen: { $first: "$isSeen" },
        invitationBy: { $first: "$invitationBy" },
        feedbackId: { $first: "$feedbackId" },
        businessId: { $first: "$businessId" },
        date: { $first: "$date" },
        time: { $first: "$time" },
        isGroup: { $first: "$isGroup" },
        isRescheduled: { $first: "$isRescheduled" },
        rescheduleBy: { $first: "$rescheduleBy" },
        status: { $first: "$status" },
        reschedule: { $first: "$reschedule" },
        createdAt: { $first: "$createdAt" },
        isPremium: { $first: "$invitationBy.isPremium" },
        deleted: { $first: "$deleted" },
      },
    },
    {
      $addFields: {
        isPremiumRecentlySent: {
          $and: [
            { $eq: ["$isPremium", true] },
            { $gte: [{ $subtract: [new Date(), "$createdAt"] }, 0] },
            {
              $lt: [
                { $subtract: [new Date(), "$createdAt"] },
                24 * 60 * 60 * 1000,
              ],
            }, // 24 hours in ms
          ],
        },
      },
    },
    { $sort: { isPremiumRecentlySent: -1, createdAt: -1 } },
    {
      $match: {
        deleted: false,
        status: aggObj.status, // Status filter if passed
        $or: [
          // If rescheduled, invitationBy == userId
          {
            $and: [
              { isRescheduled: true },
              { "invitationBy._id": mongoose.Types.ObjectId(userId) },
            ],
          },
          // If not rescheduled, check if user is in users array
          {
            $and: [
              { isRescheduled: { $ne: true } },
              { "users.userId._id": mongoose.Types.ObjectId(userId) },
            ],
          },
        ],
      },
    },
    { $skip: skipValue },
    { $limit: limit },
  ]);

  return data;
};
