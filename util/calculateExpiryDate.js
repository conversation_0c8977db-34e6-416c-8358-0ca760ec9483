// module.exports.calculateExpiryDate = (monthCount) => {
//   const currentDate = new Date();

//   const expiryDate = new Date(currentDate);
//   expiryDate.setMonth(currentDate.getMonth() + monthCount);

//   const formattedExpiryDate = expiryDate.toISOString().split("T")[0];
//   console.log("formattedExpiryDate", formattedExpiryDate);
//   return new Date(formattedExpiryDate);
// };
module.exports.calculateExpiryDate = (months) => {
  const date = new Date();
  date.setMonth(date.getMonth() + months);
  return date;
};
