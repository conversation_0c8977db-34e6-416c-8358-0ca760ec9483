// module.exports.calculateExpiryDate = (monthCount) => {
//   const currentDate = new Date();

//   const expiryDate = new Date(currentDate);
//   expiryDate.setMonth(currentDate.getMonth() + monthCount);

//   const formattedExpiryDate = expiryDate.toISOString().split("T")[0];

//   return new Date(formattedExpiryDate);
// };

module.exports.calculateExpiryTime = (months) => {
  const date = new Date();
  date.setMonth(date.getMonth() + months);
  return date;
};
