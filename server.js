const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const cookieParser = require("cookie-parser");
const http = require("http");
require("dotenv").config();

// Force server timezone to UTC for consistent time handling
process.env.TZ = "UTC";

// Verify timezone is set correctly
console.log("Server timezone:", process.env.TZ);
console.log("Current server time (UTC):", new Date().toISOString());

// Create Express app and HTTP server
const app = express();
const server = http.createServer(app);

// Import Socket.IO service
const socketService = require("./services/socketService");
const bodyParser = require("body-parser");
const fileUpload = require("express-fileupload");
const httpLogger = require("./util/createLogger");
const { subscriptionCron } = require("./cron/subscriptionCron");
//USER_ROUTE
const authRoute = require("./routes/User/authRoute");
const refreshTokenRoute = require("./routes/User/refreshTokenRoute");
const reviewRoute = require("./routes/User/reviewRoute");
const userAdsRoute = require("./routes/User/adsRoute");
const stampRoute = require("./routes/User/stampRoute");
const userRoute = require("./routes/User/userRoute");
const voucherRoute = require("./routes/User/voucherRoute");
const invitationRoute = require("./routes/User/invitationRoute");
const utilRoute = require("./routes/Common/utilRoute");
const userSubscriptionRoute = require("./routes/User/subscriptionRoute");

//PARTNER_ROUTER
const authPartnerRoute = require("./routes/Partner/authRoute");
const userPartnerRoute = require("./routes/Partner/userRoute");
const partnerAnalyticsRoute = require("./routes/Partner/analyticsRoute");
const businessRoute = require("./routes/Partner/businessRoute");
const adsRoute = require("./routes/Partner/adsRoute");
const partnerSubscriptionRoute = require("./routes/Partner/subscriptionRoute");

//ADMIN
const dashboardRoute = require("./routes/Admin/dashboardRoute");
const authAdminRoute = require("./routes/Admin/Auth/authAdminRoute");
const userAdminRoute = require("./routes/Admin/Auth/adminUserRoute");
const userAdminPartnerRoute = require("./routes/Admin/partnerRoute");
const userAdminUserrRoute = require("./routes/Admin/userRoute");
const userAnalysisRoute = require("./routes/Admin/analysisRoute");
const userAdminVoucherRoute = require("./routes/Admin/voucherRoute");
const subscriptionRoute = require("./routes/Admin/subscriptionRoute");

//COMMON
const notificationRoute = require("./routes/Common/notificationRoute");
const subscriptionPackages = require("./routes/Common/package.route");
const chatRoute = require("./routes/Common/chatRoute");

//middleware
const { checkPermission } = require("./middleware/checkPermission");
const {
  CheckPartnerPermission,
} = require("./middleware/checkPartnerPermission");
const { checkGuestAccess } = require("./middleware/checkGuestAccess");
const { checkAdminPermission } = require("./middleware/checkAdminPermission");
const ensureJsonResponse = require("./middleware/ensureJsonResponse");
const corsDebugger = require("./middleware/corsDebugger");
const { formatTimeResponse } = require("./middleware/formatTimeResponse");
const { listenWebhook } = require("./util/stripe");
const { scheduleNotificationCron } = require("./cron/notificationCron");

const { default: axios } = require("axios");

// const allowedDomains = [
//   "https://*.netme.eu",
//   "https://netme.eu",
//   "http://localhost:3000",
// ];
// console.log(
//   "allowedDomains",
//   allowedDomains.some((domain) => origin === domain) ||
//     allowedDomains.some(
//       (domain) =>
//         domain.startsWith("https://") && origin.endsWith(domain.slice(8))
//     )
// );
// Custom function to check if the origin is allowed
// const corsOptions = {
//   origin: (origin, callback) => {
//     // Allow requests with no origin (like mobile apps or curl requests)
//     if (!origin) return callback(null, true);

//     // Check if the origin is in the allowed list or is a subdomain
//     if (
//       allowedDomains.some((domain) => origin === domain) ||
//       allowedDomains.some(
//         (domain) =>
//           domain.startsWith("https://") && origin.endsWith(domain.slice(8))
//       )
//     ) {
//       callback(null, true);
//     } else {
//       callback(new Error("Not allowed by CORS"));
//     }
//   },
//   methods: ["GET", "POST", "PUT", "DELETE", "PATCH"], // Allow only GET, POST, PUT, DELETE, PATCH methods
//   credentials: true, // Allow credentials (cookies, authorization headers)
// };
// Configure CORS for all routes
app.use(
  cors({
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true,
  })
);

// Parse JSON and URL-encoded bodies
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(express.static("public"));

// Simple health check route
app.get("/", (req, res) => res.send("Working!!!"));

// Socket.IO test route
app.get("/socket-test", (req, res) => {
  res.send({
    status: true,
    message: "Socket.IO server is running",
    socketEnabled: true,
    serverTime: new Date().toISOString(),
  });
});
app.post(
  "/stripe/webhook",
  express.raw({ type: "application/json" }),
  async (req, res) => {
    const event = req.body;
    try {
      console.log(event, "EVENT_WEBHOOK");
      await listenWebhook(req, event);
      res.send(null);
    } catch (error) {
      console.log(error);
      res.status(500).send("Internal Server Error");
    }
  }
);
app.use(express.json());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cookieParser()); // Add cookie parser middleware
app.use(fileUpload());
app.use(httpLogger);

// Apply JSON response middleware to all API routes
app.use("/api", ensureJsonResponse);
app.use("/user", ensureJsonResponse);
app.use("/partner", ensureJsonResponse);
app.use("/admin", ensureJsonResponse);

// Apply time formatting middleware to all API routes
app.use("/api", formatTimeResponse);
app.use("/user", formatTimeResponse);
app.use("/partner", formatTimeResponse);
app.use("/admin", formatTimeResponse);

// import routes
const users = require("./routes/Become/user.route");
const partner = require("./routes/Become/partner.route");
const ads = require("./routes/Become/ads.route");
const voucherRoutes = require("./routes/Become/voucher.route");
const paymentRoutes = require("./routes/Become/payment.route");
const { scheduleUserCron } = require("./cron/userCron");
const {
  scheduleInviteNotificationCron,
} = require("./cron/inviteNotificationCron");
const { invitationExpire } = require("./cron/invitationExpireCron");
const dbURI = process.env.DB_URI;

const PORT = process.env.PORT;

mongoose.set("strictQuery", true);
mongoose
  .connect(dbURI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  })
  .then((result) => {
    console.log("MongoDB connected successfully");

    // Initialize Socket.IO with the HTTP server
    socketService.initialize(server);
    console.log("Socket.IO initialized with HTTP server");

    // Start the server
    server.listen(PORT, () => {
      console.log("Application Started in Port " + PORT);
      console.log("Socket.IO server is running at ws://localhost:" + PORT);
      console.log(
        "Test Socket.IO at http://localhost:" + PORT + "/socket-test.html"
      );
    });
  })
  .catch((err) => {
    console.error("MongoDB connection error:", err);
  });

// ROUTES
//WEBHOOK

//Cron
scheduleNotificationCron();
subscriptionCron();
scheduleUserCron();
scheduleInviteNotificationCron();
invitationExpire();

// use routes
app.use("/api/users", users);
app.use("/api/chat", chatRoute);
app.use("/api/partner", partner);
app.use("/api/ads", ads);
// app.use("/api/package", subscriptionPackages);
app.use("/api/voucher", voucherRoutes);
app.use("/api/payment", paymentRoutes);

// app.get("/", (req, res) => {
//   res.send("pong: its working! 😇");
// });

app.post("/api/verify", async (req, res) => {
  const { captchaValue } = req.body;
  const { data } = await axios.post(
    `https://www.google.com/recaptcha/api/siteverify?secret=${process.env.SITE_SECRET}&response=${captchaValue}`
  );
  res.send(data);
});

//USER
app.use("/api/auth", checkGuestAccess(), authRoute);
app.use("/api/refresh-token", refreshTokenRoute);

app.use("/user/ads", checkPermission(["USER"]), userAdsRoute);
app.use("/api/stamp", checkPermission(["USER"]), stampRoute);
app.use("/api/user", userRoute);
app.use("/api/user/review", checkPermission(["USER"]), reviewRoute);
app.use("/api/user/voucher", checkPermission(["USER"]), voucherRoute);
app.use(
  "/api/userSubscription",
  checkPermission(["USER"]),
  userSubscriptionRoute
);
app.use("/api/invitation", invitationRoute);

//PARTNER
app.use("/partner/auth", checkGuestAccess(), authPartnerRoute);
app.use(
  "/api/partnerSubscription",
  checkPermission(["PARTNER"]),
  partnerSubscriptionRoute
);
app.use("/partner/user", CheckPartnerPermission(["PARTNER"]), userPartnerRoute);
app.use(
  "/partner/analytics",
  CheckPartnerPermission(["PARTNER"]),
  partnerAnalyticsRoute
);
app.use(
  "/partner/business",
  CheckPartnerPermission(["PARTNER"]),
  businessRoute
);
app.use("/partner/ads", adsRoute);

//ADMIN
app.use("/api/authAdmin", checkGuestAccess(), authAdminRoute);
app.use(
  "/api/userAdmin",
  checkAdminPermission(["SUPER-ADMIN", "SUB-ADMIN"]),
  userAdminRoute
);

app.use(
  "/admin/partner",
  checkAdminPermission(["SUPER-ADMIN", "SUB-ADMIN"]),
  userAdminPartnerRoute
);
app.use(
  "/admin/dashboard",
  checkAdminPermission(["SUPER-ADMIN", "SUB-ADMIN"]),
  dashboardRoute
);

app.use(
  "/admin/user",
  checkAdminPermission(["SUPER-ADMIN", "SUB-ADMIN"]),
  userAdminUserrRoute
);
app.use(
  "/admin/voucher",
  checkAdminPermission(["SUPER-ADMIN", "SUB-ADMIN"]),
  userAdminVoucherRoute
);

app.use(
  "/admin/analysis",
  checkAdminPermission(["SUPER-ADMIN", "SUB-ADMIN"]),
  userAnalysisRoute
);
app.use(
  "/admin/subscription",
  checkAdminPermission(["SUPER-ADMIN", "SUB-ADMIN"]),
  subscriptionRoute
);
//COMMON
app.use("/api/util", utilRoute);
app.use("/api/notification", notificationRoute);
app.use("/api/package", subscriptionPackages);

// Global error handler - ensures all errors return JSON responses
app.use((err, req, res, next) => {
  console.error("Global error handler caught:", err);

  // Set appropriate status code
  const statusCode = err.statusCode || 500;

  // Always return JSON
  res.status(statusCode).json({
    error: err.message || "Internal Server Error",
    status: false,
  });
});

// 404 handler - for routes that don't exist
app.use((req, res) => {
  res.status(404).json({
    error: "Route not found",
    status: false,
  });
});
